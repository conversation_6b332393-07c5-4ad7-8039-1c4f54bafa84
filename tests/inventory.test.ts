import test from 'ava';
import { createRequire } from 'module';
import { unitsFactory } from '../src/logic/unitsFactory.js';

const require = createRequire(import.meta.url);
const consumables = require('../public/assets/data/consumables.json');
const heroes = require('../public/assets/data/heroes.json');
const units = require('../public/assets/data/unitsClasses.json');
const weapons = require('../public/assets/data/weapons.json');

test('Moving a weapon to top should match index 0', async(assert) => {
  const createUnit = unitsFactory({
    dataConsummables: consumables,
    dataHeroes: heroes,
    dataUnits: units,
    dataWeapons: weapons,
  });

  const emilie = createUnit('emilie');

  const weapon = emilie.inventory.getWeapon(1);

  emilie.inventory.moveWeaponToTop(weapon);

  assert.deepEqual(emilie.inventory.getWeapon(0), weapon);
});
