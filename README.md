# Cycles

🚧 [WIP] A turn-based game system built with [Phaser 3](http://phaser.io).

<img src='./cycles-preview.gif' height='500' />

## Roadmap

[See the projects section](https://github.com/rootasjey/cycles/projects)

## Development

### Start

You will need [Node.js](https://nodejs.org) to build the game.

* Clone the repo `git clone https://github.com/rootasjey/cycles.git`
* Navigate to you local repo `cd cycles`
* Install dependencies with `yarn` or `npm install`
* Run the game with `yarn run start` or `npm start`

## Game Specifications

### Goal

Reach one of the map objectives:

* Defeat all enemies
* Survive X days
* Defeat a specific enemy
* Rescue someone

### A player turn

* Move units
* Make units attack or use items
* End turn

## Thanks

Thanks to these awesome tools and assets, I can have fun building a game:

* [Phaser 3](http://phaser.io)
* [Kenney assets](https://www.kenney.nl)

## Licence

This project is under MIT Licence.
